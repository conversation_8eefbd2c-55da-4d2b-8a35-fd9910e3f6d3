set :application, "images-optimization"
set :scm, :tarball
set :deploy_to, "/home/<USER>/www/images-optimization"
set :pty, true
set :keep_releases, 5
set :full_console_path, "#{deploy_to}/releases/#{release_timestamp}"
set :linked_files, ["client/.env"]
before "deploy:cleanup", "deploy:reload_app"
before "deploy:cleanup_rollback", "deploy:reload_app"

namespace :deploy do
    task :reload_app do
            on roles(:app) do
                execute("cd #{fetch(:deploy_to)}/current/client && pnpm install")
                execute("cd #{fetch(:deploy_to)}/current/server && pnpm install")
                execute("pm2 startOrReload #{fetch(:deploy_to)}/current/pm2.config.js --update-env")
                execute("pm2 save")
            end
        end
    end
