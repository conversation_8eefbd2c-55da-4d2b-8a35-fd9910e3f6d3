#!/usr/bin/env groovy

@Library('shared-libs') _

pipeline {
  agent 'any'

  options {
    skipDefaultCheckout true
  }

  stages {

    stage("Checkout Code") {
      steps {
        checkout scm
      }
    }

    stage('Check styles') {
      steps{
        echo 'No tests.'
      }
    }

  }

  post {
    success {
      script {
        currentBuild.result = currentBuild.result ?: 'SUCCESS'
        notifyBitbucket()
      }
    }

    failure {
      script {
        currentBuild.result = currentBuild.result ?: 'FAILED'
        notifyBitbucket()
      }
    }

    always {
      deleteDir()
    }
  }

}
