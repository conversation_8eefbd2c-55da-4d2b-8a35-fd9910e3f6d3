{"name": "images-optimisation-client", "private": true, "version": "2.0.0", "type": "module", "scripts": {"dev": "vite", "build": "pnpm i && tsc && vite build", "preview": "vite preview", "serve": "serve dist -l $VITE_PORT", "start": "pnpm run build && dotenv -- pnpm run serve", "lint": "biome lint", "lint:ts": "tsc --noEmit --skipLib<PERSON>heck --incremental --project ./tsconfig.app.json", "migrate": "biome migrate --write"}, "dependencies": {"betgames-contract-js": "bitbucket:tvzaidimai/contract-js#4.15.0", "classnames": "2.5.1", "lodash-es": "4.17.21", "react": "19.1.1", "react-dom": "19.1.1", "tslib": "2.8.1", "zustand": "5.0.7"}, "devDependencies": {"@biomejs/biome": "2.1.4", "@types/lodash-es": "4.17.12", "@types/node": "24.2.1", "@types/react": "19.1.10", "@types/react-dom": "19.1.7", "@vitejs/plugin-react-swc": "4.0.0", "globals": "16.3.0", "serve": "14.2.4", "typescript": "5.9.2", "vite": "7.1.2"}, "packageManager": "pnpm@10.14.0"}