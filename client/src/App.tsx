import { JSX, useEffect } from 'react';
import { Content } from './components/Content';
import { Form } from './components/Form';
import { ImagesDownloader } from './components/ImagesDownloader';
import { ImagesValidator } from './components/ImagesValidator';
import { Output } from './components/Output';
import { Preview } from './components/Preview';
import { useForm } from './state/useForm.ts';
import './App.css';

export const App = (): JSX.Element => {
    const toggleAdvanced = useForm((state) => state.toggleAdvanced);

    useEffect(() => {
        const keyHandler = (event: KeyboardEvent) => {
            if (event.code === 'KeyX') {
                toggleAdvanced();
            }
        };

        window.addEventListener('keydown', keyHandler);

        return () => {
            window.removeEventListener('keydown', keyHandler);
        };
    }, [toggleAdvanced]);

    return (
        <Content>
            <Form />
            <ImagesValidator />
            <ImagesDownloader />
            <Output />
            <Preview />
        </Content>
    );
};
