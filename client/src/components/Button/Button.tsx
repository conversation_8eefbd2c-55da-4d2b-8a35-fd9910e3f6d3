import { JSX, PropsWithChildren } from 'react';
import classNames from 'classnames';

interface IProps {
    type?: 'button' | 'submit';
    styleType?: 'primary' | 'danger';
    disabled?: boolean;
    className?: string;
    onClick?(value: unknown): void;
}

export const Button = ({
    type = 'button',
    styleType = 'primary',
    disabled = false,
    className,
    onClick,
    children,
}: PropsWithChildren<IProps>): JSX.Element => (
    <button
        type={type}
        disabled={disabled}
        className={classNames(`btn btn-${styleType}`, className)}
        onClick={
            onClick
                ? (event) => {
                      onClick(event.currentTarget.value);
                  }
                : undefined
        }
    >
        {children}
    </button>
);
