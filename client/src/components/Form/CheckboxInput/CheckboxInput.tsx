import { Fragment, JSX } from 'react';

interface IOption {
    value: string | number;
    name?: string;
    checked: boolean;
}

interface IProps {
    name: string;
    label: string;
    options: IOption[];
    onChange(value: (string | number)[]): void;
}

export const CheckboxInput = ({ name, label, options, onChange }: IProps): JSX.Element => (
    <div>
        <label className="d-block">{label}:</label>
        <div className="d-flex flex-wrap gap-1">
            {options.map((option) => {
                const key = `${name}-${option.value}`;

                return (
                    <Fragment key={key}>
                        <input
                            id={key}
                            type="checkbox"
                            name={`${name}[]`}
                            value={option.value}
                            checked={option.checked}
                            onChange={() => {
                                const optionChangeIndex = options.findIndex(
                                    (opt) => opt.value === option.value,
                                );
                                const optionChange = options[optionChangeIndex];

                                const newOptions = [...options];
                                newOptions[optionChangeIndex] = {
                                    ...optionChange,
                                    checked: !optionChange.checked,
                                };

                                onChange(
                                    newOptions
                                        .filter((newOption) => newOption.checked)
                                        .map((newOption) => newOption.value),
                                );
                            }}
                            className="btn-check"
                        />
                        <label htmlFor={key} className="btn btn-outline-primary">
                            {option.name ?? option.value}
                        </label>
                    </Fragment>
                );
            })}
        </div>
    </div>
);
