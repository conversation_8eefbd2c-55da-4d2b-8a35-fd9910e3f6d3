import { ComponentRef, JSX, useRef, useState } from 'react';
import classNames from 'classnames';
import { useForm } from '../../../state/useForm';
import { useOutput } from '../../../state/useOutput';
import './DropArea.css';

export const DropArea = (): JSX.Element => {
    const [dragOver, setDragOver] = useState<boolean>(false);
    const setImages = useForm((state) => state.setImages);
    const clearOutput = useOutput((state) => state.clearOutput);
    const fileInputRef = useRef<ComponentRef<'input'>>(null);

    const setImagesFiles = (images: File[]): void => {
        setImages(images);
        clearOutput();
    };

    return (
        <>
            <input
                ref={fileInputRef}
                type="file"
                name="images[]"
                multiple
                className="d-none"
                onChange={(event) => {
                    if (event.target.files) {
                        setImagesFiles(Array.from(event.target.files));
                    }
                }}
            />
            <div
                id="drop-area"
                className={classNames(
                    'align-items-center border border-2 d-flex drop-area flex-column gap-3 h-100 justify-content-center p-3 rounded-3',
                    { 'bg-body-secondary': dragOver },
                )}
                onPointerEnter={() => {
                    setDragOver(true);
                }}
                onPointerLeave={() => {
                    setDragOver(false);
                }}
                onDragOver={(event) => {
                    event.preventDefault();
                    setDragOver(true);
                }}
                onDragLeave={() => {
                    setDragOver(false);
                }}
                onDrop={(event) => {
                    event.preventDefault();
                    setDragOver(false);

                    if (event.dataTransfer.files) {
                        setImagesFiles(Array.from(event.dataTransfer.files));
                    }
                }}
            >
                <div>Drag & Drop your image here</div>
                <button
                    type="button"
                    className="btn btn-primary"
                    onClick={() => {
                        fileInputRef.current!.click();
                    }}
                >
                    or browse
                </button>
            </div>
        </>
    );
};
