import { JS<PERSON> } from 'react';
import { isEqual } from 'lodash-es';
import {
    API_URL,
    AVAILABLE_FORMATS,
    AVAILABLE_RESOLUTIONS,
    MAX_QUALITY,
    MIN_QUALITY,
} from '../../constants';
import { useDownloadList } from '../../state/useDownloadList';
import { IFormState, useForm } from '../../state/useForm';
import { useOutput } from '../../state/useOutput';
import { Button } from '../Button';
import { IImageConfigKey, IMAGE_CONFIG } from '../ImagesValidator/utils/config';
import { CheckboxInput } from './CheckboxInput';
import { DropArea } from './DropArea';
import { Select } from './Select';
import { TextInput } from './TextInput';
import './Form.css';

const CUSTOM_DROPDOWN_VALUE = '-1';
const ORIGINAL_SIZE_DROPDOWN_VALUE = '-2';

const DROPDOWN_VALUES = Object.keys(IMAGE_CONFIG).map((key) => ({
    name: key,
    value: key,
}));

const dropdownOptions = DROPDOWN_VALUES.concat([
    {
        name: '-- Original size --',
        value: ORIGINAL_SIZE_DROPDOWN_VALUE,
    },
    {
        name: '-- Custom --',
        value: CUSTOM_DROPDOWN_VALUE,
    },
]);

export const Form = (): JSX.Element => {
    const {
        form,
        updateForm,
        setQuality,
        setFormats,
        setFileName,
        clearForm,
        setProcessing,
        advanced,
    } = useForm();
    const { setOutput, clearOutput } = useOutput();
    const { appendDownloadList, clearDownloadList } = useDownloadList();

    const isValid =
        form.images.length &&
        form.fileNames.length === form.images.length &&
        form.fileNames.every((fileName) => fileName.trim()) &&
        form.resolutions.length &&
        form.formats.length;
    const preset = DROPDOWN_VALUES.find(({ value }) => {
        if (value === ORIGINAL_SIZE_DROPDOWN_VALUE) {
            return;
        }

        const config = IMAGE_CONFIG[value as IImageConfigKey];
        return (
            config.path === form.imagesPath &&
            isEqual(config.sizes, form.resolutions) &&
            isEqual(config.formats, form.formats)
        );
    });

    return (
        <form
            className="d-grid form gap-3"
            encType="multipart/form-data"
            onSubmit={async (event) => {
                event.preventDefault();

                const formData = new FormData();

                form.images.forEach((imageFile) => {
                    formData.append('images[]', imageFile);
                });

                formData.append('imagesPath', form.imagesPath);
                formData.append('branded', form.branded);

                form.fileNames.forEach((fileName) => {
                    formData.append('fileNames[]', fileName);
                });

                form.resolutions.forEach((resolution) => {
                    formData.append('resolutions[]', resolution.toString());
                });
                form.formats.forEach((format) => {
                    formData.append('formats[]', format);

                    if (advanced && form.qualities[format] !== undefined) {
                        formData.append(`qualities[${format}]`, String(form.qualities[format]));
                    }
                });

                clearOutput();
                setProcessing(true);

                let response: Response;

                try {
                    response = await fetch(`${API_URL}/upload`, {
                        method: 'POST',
                        body: formData,
                    });
                } catch (error) {
                    console.error(error);
                    setOutput((error as Error).message);
                    return;
                }

                if (!response.ok) {
                    setOutput(`${response.statusText} (${response.status})`);
                    return;
                }

                const result = await response.json();

                if (result.error) {
                    setOutput(result.error);
                } else if (result.success) {
                    setOutput(result.files.join('\n'));
                    appendDownloadList(result.files);
                }

                setProcessing(false);
            }}
        >
            <DropArea />

            <div className="d-flex flex-column gap-3">
                <Select
                    label="Preset"
                    selected={preset?.value ?? CUSTOM_DROPDOWN_VALUE}
                    options={dropdownOptions}
                    onChange={(value) => {
                        const newForm: Partial<IFormState['form']> = {};

                        if (value === CUSTOM_DROPDOWN_VALUE) {
                            newForm.imagesPath = '';
                        } else if (value === ORIGINAL_SIZE_DROPDOWN_VALUE) {
                            newForm.resolutions = [0];
                        } else {
                            newForm.imagesPath = IMAGE_CONFIG[value as IImageConfigKey].path;
                            newForm.resolutions = IMAGE_CONFIG[value as IImageConfigKey].sizes;
                            newForm.formats = IMAGE_CONFIG[value as IImageConfigKey].formats;
                        }

                        updateForm(newForm);
                    }}
                />
                <TextInput
                    name="imagesPath"
                    label="Image path"
                    value={form.imagesPath}
                    onChange={(value) => {
                        updateForm({ imagesPath: value });
                    }}
                />
                <TextInput
                    name="branded"
                    label="Branded"
                    value={form.branded}
                    required={false}
                    onChange={(value) => {
                        updateForm({ branded: value });
                    }}
                />
                {form.fileNames.map((fileName, index) => (
                    <TextInput
                        key={form.images[index].name}
                        name="fileName"
                        label={`Image file name (${index + 1})`}
                        value={fileName}
                        onChange={(value) => {
                            setFileName(value, index);
                        }}
                    />
                ))}
                <CheckboxInput
                    name="resolutions"
                    label="Resolutions"
                    options={AVAILABLE_RESOLUTIONS.map((resolution) => ({
                        value: resolution,
                        name: resolution === 0 ? 'Original' : `${resolution}`,
                        checked: form.resolutions.includes(resolution),
                    }))}
                    onChange={(options) => {
                        updateForm({
                            resolutions: options as typeof AVAILABLE_RESOLUTIONS,
                        });
                    }}
                />
                <CheckboxInput
                    name="formats"
                    label="Formats"
                    options={AVAILABLE_FORMATS.map((format) => ({
                        value: format,
                        name: format.toUpperCase(),
                        checked: form.formats.includes(format),
                    }))}
                    onChange={setFormats}
                />
                {advanced &&
                    form.formats.map((format) => (
                        <TextInput
                            key={format}
                            type="number"
                            label={`Quality (${format.toUpperCase()})`}
                            name={`qualities[${format}]`}
                            value={form.qualities[format]}
                            onChange={(value) => {
                                setQuality(format, Number(value));
                            }}
                            min={MIN_QUALITY}
                            max={MAX_QUALITY}
                            step={1}
                        />
                    ))}
                <div className="d-flex gap-1">
                    <Button type="submit" disabled={!isValid}>
                        Upload
                    </Button>
                    {/* TODO <Button>Add to queue</Button> */}
                    <Button
                        type="button"
                        styleType="danger"
                        onClick={() => {
                            clearOutput();
                            clearForm();
                            clearDownloadList();
                        }}
                    >
                        Reset form
                    </Button>
                </div>
            </div>
        </form>
    );
};
