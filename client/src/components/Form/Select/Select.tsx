import { JSX } from 'react';

interface IOption {
    value: string | number;
    name?: string;
}

interface IProps extends Pick<JSX.IntrinsicElements['select'], 'name' | 'id'> {
    label: string;
    selected: IOption['value'];
    options: IOption[];
    onChange(value: IOption['value']): void;
}

export const Select = ({ id, name, label, selected, options, onChange }: IProps): JSX.Element => (
    <div>
        <label htmlFor={name} className="d-block">
            {label}:
        </label>
        <select
            id={id ?? name}
            name={name}
            className="form-select"
            value={selected}
            onChange={(e) => {
                onChange(e.target.value as IOption['value']);
            }}
        >
            {options.map((option) => (
                <option key={option.value} value={option.value}>
                    {option.name ?? option.value}
                </option>
            ))}
        </select>
    </div>
);
