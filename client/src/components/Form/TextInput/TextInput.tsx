import classNames from 'classnames';
import { JSX } from 'react';

interface IProps
    extends Pick<
        JSX.IntrinsicElements['input'],
        'type' | 'id' | 'name' | 'value' | 'min' | 'max' | 'step'
    > {
    label: string;
    required?: boolean;
    onChange(value: string): void;
}

export const TextInput = ({
    label,
    type = 'text',
    id,
    name,
    value,
    required = true,
    onChange,
    min,
    max,
    step,
}: IProps): JSX.Element => (
    <div>
        <label htmlFor={name} className="d-block">
            {label}:
        </label>
        <input
            type={type}
            id={id || name}
            name={name}
            value={value}
            onChange={(event) => {
                onChange(event.target.value);
            }}
            className={classNames('form-control', {
                'is-invalid': required && !String(value || '').trim(),
            })}
            min={min}
            max={max}
            step={step}
        />
    </div>
);
