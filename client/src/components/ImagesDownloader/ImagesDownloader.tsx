import { JSX } from 'react';
import { useDownloadList } from '../../state/useDownloadList';
import { Button } from '../Button';
import { fileDownloader } from './utils/FileDownloader';

export const ImagesDownloader = (): JSX.Element | null => {
    const { downloadList } = useDownloadList();

    if (!downloadList.length) {
        return null;
    }

    return (
        <div className="align-items-center d-flex flex-wrap gap-3">
            <Button
                type="button"
                onClick={async () => {
                    await fileDownloader.downloadZip({
                        list: downloadList,
                    });
                }}
            >
                Download
            </Button>
            <ul className="list-group w-100">
                {downloadList.map((item) => (
                    <li key={item} className="list-group-item">
                        {item}
                    </li>
                ))}
            </ul>
        </div>
    );
};
