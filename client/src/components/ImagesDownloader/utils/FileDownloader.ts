import { API_URL } from '../../../constants';

interface DownloadProgress {
    loaded: number;
    total: number;
    progress: number;
}

interface DownloadOptions {
    fileName?: string;
    list: string[];
    onProgress?: (progress: DownloadProgress) => void;
    onComplete?: (blob: Blob) => void;
    onError?: (error: Error) => void;
}

class FileDownloader {
    async downloadZip(options: DownloadOptions): Promise<void> {
        try {
            const response = await fetch(`${API_URL}/download`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ images: options.list }),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            if (!response.body) {
                throw new Error('No response body received');
            }

            const reader = response.body.getReader();
            const contentLength = Number(response.headers.get('Content-Length') ?? '0');
            const fileName = options.fileName || this.getFileName(response) || 'files.zip';

            // Create a new ReadableStream to handle the response data
            const stream = new ReadableStream({
                async start(controller) {
                    let totalLoaded = 0;

                    try {
                        while (true) {
                            const { done, value } = await reader.read();

                            if (done) {
                                controller.close();
                                break;
                            }

                            totalLoaded += value.length;

                            // Report progress
                            if (options.onProgress) {
                                options.onProgress({
                                    loaded: totalLoaded,
                                    total: contentLength,
                                    progress: (totalLoaded / contentLength) * 100,
                                });
                            }

                            controller.enqueue(value);
                        }
                    } catch (error) {
                        controller.error(error);
                    }
                },
            });

            // Convert stream to blob
            const blob = await new Response(stream).blob();

            if (options.onComplete) {
                options.onComplete(blob);
            }

            // Trigger download
            this.saveBlob(blob, fileName);
        } catch (error) {
            if (options.onError && error instanceof Error) {
                options.onError(error);
            }
            throw error;
        }
    }

    private getFileName(response: Response): string | null {
        const disposition = response.headers.get('Content-Disposition');

        if (!disposition) {
            return null;
        }

        const filenameMatch = disposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (!filenameMatch) {
            return null;
        }

        const filename = filenameMatch[1].replace(/['"]/g, '');

        return decodeURIComponent(filename);
    }

    private saveBlob(blob: Blob, fileName: string): void {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;

        // Append to document temporarily
        document.body.appendChild(link);
        link.click();

        // Cleanup
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    }
}

export const fileDownloader = new FileDownloader();
