import { JSX, useEffect, useState } from 'react';
import { Button } from '../Button';
import { useForm } from '../../state/useForm';
import { useOutput } from '../../state/useOutput';
import { validator } from './utils/Validator';

export const ImagesValidator = (): JSX.Element | null => {
    const images = useForm((state) => state.form.images);
    const { setOutput, appendOutput } = useOutput();
    const [validating, setValidating] = useState<boolean>(false);

    useEffect(() => {
        if (!validating) {
            return;
        }

        const list = validator.createList();

        setOutput([`Images count: ${list.length}`, ''].join('\n'));

        validator
            .start(list, (url, status) => {
                appendOutput(`\n${status ? '✅' : '❌'} ${url}`);
            })
            .then(() => {
                setValidating(false);
            });

        return () => {
            validator.stop();
        };
    }, [validating]);

    if (images.length) {
        return null;
    }

    return (
        <div className="align-items-center d-flex gap-3">
            <Button
                type="button"
                onClick={() => {
                    setValidating((state) => !state);
                }}
            >
                {validating ? 'Stop' : 'Validate URLs'}
            </Button>
            {validating && <span>Processing...</span>}
        </div>
    );
};
