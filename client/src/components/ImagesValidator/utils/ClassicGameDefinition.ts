import { GameDefinition } from 'betgames-contract-js';
import { GameId } from '../../../enums';

export class ClassicGameDefinition extends GameDefinition {
    public static readonly THIRD_PARTY_GAMES_IDS = [
        ...GameDefinition.PLS_GAMES_IDS,
        ...GameDefinition.HZ_GAMES_IDS,
    ];

    public static readonly LOTTERY_GAMES_IDS = [
        GameId.LUCKY5,
        GameId.LUCKY6,
        GameId.LUCKY7,
        GameId.DICE_DUEL,
        GameId.WHEEL,
        GameId.BETWAY_LUCKY7,
        GameId.RNG_LUCKY7,
    ];

    public static readonly SECTORS_GAMES_IDS = [
        GameId.DICE_DUEL,
        GameId.LUCKY6,
        GameId.RNG_FOOTBALL_GRID,
    ];

    public static readonly HISTORY_ROUNDS_GAMES_IDS = [
        GameId.POKER,
        GameId.STS_POKER,
        GameId.BACCARAT,
        GameId.SIX_PLUS_POKER,
    ];

    public static readonly CASINO_VIEW_GAMES_IDS = [
        GameId.WHEEL,
        GameId.RNG_WHEEL,
        GameId.DICE_DUEL,
        GameId.LUCKY_KICKS,
        // TODO: WoB, BoP will be added later
    ];

    public static readonly BRAZIL_MUTED_GAMES_IDS = [
        GameId.LUCKY7,
        GameId.LUCKY5,
        GameId.DICE_DUEL,
        GameId.WHEEL,
        GameId.POKER,
        GameId.SIX_PLUS_POKER,
        GameId.RNG_WHEEL,
        GameId.ROULETTE_EURO_LT,
        GameId.ROULETTE_FLASH_LT,
        GameId.STARZLE,
        GameId.GIZA,
    ];

    public static readonly NEW_GAMES_IDS = [
        GameId.ROULETTE_FLASH_LT,
        GameId.LUCKY_KICKS,
        GameId.STARZLE,
        GameId.GIZA,
        GameId.BAD_BABOONS_DELUXE,
        GameId.BAD_BABOONS_DELUXE_RTP_94,
        GameId.BAD_BABOONS_DELUXE_RTP_96,
        GameId.DRAGONS_FATE,
        GameId.DRAGONS_FATE_RTP_94,
        GameId.DRAGONS_FATE_RTP_96,
        GameId.LUCKY_LOKI,
        GameId.LUCKY_LOKI_RTP_94,
        GameId.LUCKY_LOKI_RTP_96,
        GameId.PENGUIN_RUSH,
        GameId.PENGUIN_RUSH_HWB,
        GameId.PENGUIN_RUSH_NEW_THEME,
        GameId.FOREST_FRENZY,
        GameId.FOREST_FRENZY_RTP_94,
        GameId.FOREST_FRENZY_RTP_96,
        GameId.GIZA_SLOT_DELUXE,
        GameId.GIZA_SLOT_DELUXE_RTP_94,
        GameId.GIZA_SLOT_DELUXE_RTP_96,
        GameId.RGS_ROULETTE_EURO,
        GameId.SKYWARD_DELUXE,
        GameId.SKYWARD_100,
        GameId.PENGUIN_RUSH_OLIMP_RED_RTP_96,
        GameId.PENGUIN_RUSH_ONE_WIN_RTP_96,
        GameId.PENGUIN_RUSH_SPORTPESA_RTP_96,
        GameId.LOTUS_WEALTH_WONDERS_DELUXE,
        GameId.LOTUS_WEALTH_WONDERS_DELUXE_RTP_94,
        GameId.LOTUS_WEALTH_WONDERS_DELUXE_RTP_96,
        GameId.GOLDEN_ADVENTURE,
        GameId.GOLDEN_ADVENTURE_RTP_94,
        GameId.GOLDEN_ADVENTURE_RTP_96,
        GameId.LUCKY_LENNY,
        GameId.DRAGONS_FATE_ONE_WIN,
    ];

    public static readonly WHEEL_GAMES_IDS = [GameId.WHEEL, GameId.RNG_WHEEL];

    public static readonly GAMES_IDS_ALIASES = {
        [GameId.POKER]: [GameId.STS_POKER],
        [GameId.LUCKY7]: [GameId.BETWAY_LUCKY7, GameId.RNG_LUCKY7],
        [GameId.WHEEL]: [GameId.RNG_WHEEL],
        [GameId.SKYWARD]: [
            ...ClassicGameDefinition.SKYWARD_GAMES_IDS.filter((id) => id !== GameId.SKYWARD),
        ],
    };

    private static readonly SKYWARD_NAME_MAP: Partial<Record<GameId, string>> = {
        [GameId.SKYWARD_100]: 'skyward-100',
        // Add more uniq Skyward game names if needed
    };

    public static readonly GAMES_NAMES: Record<GameId, string> = {
        ...ClassicGameDefinition.SKYWARD_GAMES_IDS.reduce(
            (result, id) => ({
                ...result,
                [id]: ClassicGameDefinition.SKYWARD_NAME_MAP[id] ?? 'skyward',
            }),
            {} as Record<GameId, string>,
        ),
        [GameId.LUCKY7]: 'lucky7',
        [GameId.LUCKY6]: 'lucky6',
        [GameId.LUCKY5]: 'lucky5',
        [GameId.DICE_DUEL]: 'dice-duel',
        [GameId.RNG_FOOTBALL_GRID]: 'rng-football-grid',
        [GameId.WHEEL]: 'wheel',
        [GameId.RNG_WHEEL]: 'rng-wheel',
        [GameId.POKER]: 'poker',
        [GameId.BACCARAT]: 'baccarat',
        [GameId.WAR]: 'war',
        [GameId.SIX_PLUS_POKER]: 'headsup',
        [GameId.STS_POKER]: 'sts-poker',
        [GameId.BETWAY_LUCKY7]: 'betway-lucky7',
        [GameId.RNG_LUCKY7]: 'rng-lucky7',
        [GameId.RNG_ROULETTE]: 'rng-roulette',
        [GameId.SHOVA52]: 'shova52',
        [GameId.ROULETTE_EURO_LT]: 'roulette-euro-lt',
        [GameId.ROULETTE_AMERICAN_GE]: 'roulette-american-ge',
        [GameId.ROULETTE_EURO_GE]: 'roulette-euro-ge',
        [GameId.ROULETTE_FLASH_LT]: 'roulette-flash-lt',
        [GameId.RGS_ROULETTE_EURO]: 'rgs-roulette-euro',
        [GameId.LUCKY_KICKS]: 'lucky-kicks',
        [GameId.PLS_BLACKJACK_T1]: 'pls-blackjack-t1',
        [GameId.PLS_BLACKJACK_T2]: 'pls-blackjack-t2',
        [GameId.PLS_BLACKJACK_T3]: 'pls-blackjack-t3',
        [GameId.PLS_BLACKJACK_T4]: 'pls-blackjack-t4',
        [GameId.PLS_BLACKJACK_T5]: 'pls-blackjack-t5',
        [GameId.PLS_BLACKJACK_T6]: 'pls-blackjack-t6',
        [GameId.PLS_BLACKJACK_T7]: 'pls-blackjack-t7',
        [GameId.PLS_BLACKJACK_T8]: 'pls-blackjack-t8',
        [GameId.PLS_BLACKJACK_T9]: 'pls-blackjack-t9',
        [GameId.PLS_BLACKJACK_T10]: 'pls-blackjack-t10',
        [GameId.PLS_BLACKJACK_T11]: 'pls-blackjack-t11',
        [GameId.PLS_BLACKJACK_T12]: 'pls-blackjack-t12',
        [GameId.PLS_BLACKJACK_T13]: 'pls-blackjack-t13',
        [GameId.PLS_BLACKJACK_T14]: 'pls-blackjack-t14',
        [GameId.PLS_BLACKJACK_T15]: 'pls-blackjack-t15',
        [GameId.STARZLE]: 'starzle',
        [GameId.GIZA]: 'giza',
        [GameId.CITRUS_SPLASH]: 'citrus-splash',
        [GameId.CANDY_COINS]: 'candy-coins',
        [GameId.CANDY_COINS_SUNBET]: 'candy-coins-sunbet',
        [GameId.LOTUS_WEALTH_WONDERS]: 'lotus',
        [GameId.GIZA_SLOT]: 'giza-2',
        [GameId.GIZA_SLOT_RTP_94]: 'giza-2',
        [GameId.GIZA_SLOT_RTP_96]: 'giza-2',
        [GameId.CITRUS_SPLASH_HWB]: 'citrus-splash-hwb',
        [GameId.CITRUS_SPLASH_BETWAY]: 'citrus-splash-betway',
        [GameId.CANDY_COINS_HWB]: 'candy-coins-hwb',
        [GameId.CANDY_COINS_BETWAY]: 'candy-coins-betway',
        [GameId.LOTUS_WEALTH_WONDERS_HWB]: 'lotus-hwb',
        [GameId.LOTUS_WEALTH_WONDERS_BETWAY]: 'lotus-betway',
        [GameId.LOTUS_WEALTH_WONDERS_DELUXE]: 'lotus-deluxe',
        [GameId.LOTUS_WEALTH_WONDERS_DELUXE_RTP_94]: 'lotus-deluxe',
        [GameId.LOTUS_WEALTH_WONDERS_DELUXE_RTP_96]: 'lotus-deluxe',
        [GameId.GIZA_SLOT_BETWAY]: 'giza-2',
        [GameId.GIZA_SLOT_OLIMP]: 'giza-2',
        [GameId.BAD_BABOONS]: 'bad-baboons',
        [GameId.BAD_BABOONS_RTP_94]: 'bad-baboons',
        [GameId.BAD_BABOONS_RTP_96]: 'bad-baboons',
        [GameId.BAD_BABOONS_BETWAY]: 'bad-baboons-betway',
        [GameId.BAD_BABOONS_DELUXE]: 'bad-baboons-deluxe',
        [GameId.BAD_BABOONS_DELUXE_RTP_94]: 'bad-baboons-deluxe',
        [GameId.BAD_BABOONS_DELUXE_RTP_96]: 'bad-baboons-deluxe',
        [GameId.PENGUIN_RUSH]: 'penguin-rush',
        [GameId.PENGUIN_RUSH_NEW_THEME]: 'penguin-rush-new-theme',
        [GameId.PENGUIN_RUSH_HWB]: 'penguin-rush-hwb',
        [GameId.PENGUIN_RUSH_OLIMP_RED_RTP_96]: 'penguin-rush-olimp-red',
        [GameId.PENGUIN_RUSH_ONE_WIN_RTP_96]: 'penguin-rush-one-win',
        [GameId.PENGUIN_RUSH_SPORTPESA_RTP_96]: 'penguin-rush-sportpesa',
        [GameId.DRAGONS_FATE]: 'dragons-fate',
        [GameId.DRAGONS_FATE_RTP_94]: 'dragons-fate',
        [GameId.DRAGONS_FATE_RTP_96]: 'dragons-fate',
        [GameId.LUCKY_LOKI]: 'lucky-loki',
        [GameId.LUCKY_LOKI_RTP_94]: 'lucky-loki',
        [GameId.LUCKY_LOKI_RTP_96]: 'lucky-loki',
        [GameId.GIZA_SLOT_DELUXE]: 'giza-slot-deluxe',
        [GameId.GIZA_SLOT_DELUXE_RTP_94]: 'giza-slot-deluxe',
        [GameId.GIZA_SLOT_DELUXE_RTP_96]: 'giza-slot-deluxe',
        [GameId.GOLDEN_ADVENTURE]: 'golden-adventure',
        [GameId.GOLDEN_ADVENTURE_RTP_94]: 'golden-adventure',
        [GameId.GOLDEN_ADVENTURE_RTP_96]: 'golden-adventure',
        [GameId.FOREST_FRENZY]: 'forest-frenzy',
        [GameId.FOREST_FRENZY_RTP_94]: 'forest-frenzy',
        [GameId.FOREST_FRENZY_RTP_96]: 'forest-frenzy',
        [GameId.LUCKY_LENNY]: 'lucky-lenny',
        [GameId.GIZA_SLOT_HWB]: 'giza-slot-hwb',
        [GameId.DRAGONS_FATE_ONE_WIN]: 'dragons-fate-one-win',
        [GameId.CITRUS_SPLASH_SUNBET]: 'citrus-splash-sunbet',
        [GameId.BUFFALO_WILDS]: 'buffalo-wilds',
        [GameId.BUFFALO_WILDS_RTP_94]: 'buffalo-wilds',
        [GameId.BUFFALO_WILDS_RTP_96]: 'buffalo-wilds',
        [GameId.OLYMPUS_GOLD]: 'olympus-gold',
        [GameId.OLYMPUS_GOLD_RTP_94]: 'olympus-gold',
        [GameId.OLYMPUS_GOLD_RTP_96]: 'olympus-gold',
    };

    public static readonly GAMES_WITH_PAYOUT_POPUP: GameId[] = [
        GameId.ROULETTE_FLASH_LT,
        GameId.RNG_ROULETTE,
        GameId.ROULETTE_EURO_LT,
        GameId.RGS_ROULETTE_EURO,
        GameId.LUCKY_KICKS,
        GameId.STARZLE,
        GameId.GIZA,
        GameId.DICE_DUEL,
        GameId.SIX_PLUS_POKER,
        GameId.LUCKY7,
        GameId.POKER,
        GameId.LUCKY5,
        GameId.RNG_LUCKY7,
        ...ClassicGameDefinition.WHEEL_GAMES_IDS,
        ...ClassicGameDefinition.SKYWARD_GAMES_IDS,
    ];

    public static readonly GAMES_WITH_CHIPS: GameId[] = [
        ...this.ROULETTE_GAMES_IDS,
        ...this.CASINO_VIEW_GAMES_IDS,
        GameId.SHOVA52,
        GameId.GIZA,
        GameId.STARZLE,
        GameId.RGS_ROULETTE_EURO,
    ];

    public static readonly GAMES_WITHOUT_RESULTS: GameId[] = [
        ...this.THIRD_PARTY_GAMES_IDS,
        GameId.RGS_ROULETTE_EURO,
        GameId.LUCKY_LENNY,
    ];

    public static isThirdPartyGame(gameId: GameId): boolean {
        return ClassicGameDefinition.THIRD_PARTY_GAMES_IDS.includes(gameId);
    }

    public static isChipsGame(gameId: GameId): boolean {
        return ClassicGameDefinition.GAMES_WITH_CHIPS.includes(gameId);
    }

    public static isLottery(gameId: GameId): boolean {
        return ClassicGameDefinition.LOTTERY_GAMES_IDS.includes(gameId);
    }

    public static isWithSectors(gameId: GameId): boolean {
        return ClassicGameDefinition.SECTORS_GAMES_IDS.includes(gameId);
    }

    public static isNew(gameId: GameId): boolean {
        return ClassicGameDefinition.NEW_GAMES_IDS.includes(gameId);
    }

    public static isCasinoView(gameId: GameId): boolean {
        return ClassicGameDefinition.CASINO_VIEW_GAMES_IDS.includes(gameId);
    }

    public static isWithHistoryRounds(gameId: GameId): boolean {
        return ClassicGameDefinition.HISTORY_ROUNDS_GAMES_IDS.includes(gameId);
    }

    public static getGameIdAlias(gameId: GameId): GameId {
        // @ts-ignore
        const [alias] = Object.entries(ClassicGameDefinition.GAMES_IDS_ALIASES).find(([, ids]) =>
            (ids as GameId[]).includes(gameId),
        );
        return (alias as unknown as GameId) ?? gameId;
    }

    public static isLucky6(gameId: GameId): boolean {
        return gameId === GameId.LUCKY6;
    }

    public static isDiceDuel(gameId: GameId): boolean {
        return gameId === GameId.DICE_DUEL;
    }

    public static isInstantLucky7(gameId: GameId): boolean {
        return gameId === GameId.RNG_LUCKY7;
    }

    public static isFootballGrid(gameId: GameId): boolean {
        return gameId === GameId.RNG_FOOTBALL_GRID;
    }

    public static isShova52(gameId: GameId): boolean {
        return gameId === GameId.SHOVA52;
    }

    public static isLuckyKicks(gameId: GameId): boolean {
        return gameId === GameId.LUCKY_KICKS;
    }

    public static isWheelType(gameId: GameId): boolean {
        return ClassicGameDefinition.WHEEL_GAMES_IDS.includes(gameId);
    }

    public static isPokerType(gameId: GameId): boolean {
        return [GameId.POKER, GameId.SIX_PLUS_POKER].includes(gameId);
    }

    public static isRoulette(gameId: GameId): boolean {
        return ClassicGameDefinition.ROULETTE_GAMES_IDS.includes(gameId);
    }

    public static isEuroRoulette(gameId: GameId): boolean {
        return (
            ClassicGameDefinition.ROULETTE_GAMES_IDS.includes(gameId) &&
            gameId !== GameId.ROULETTE_AMERICAN_GE
        );
    }

    public static isAmericanRoulette(gameId: GameId): boolean {
        return gameId === GameId.ROULETTE_AMERICAN_GE;
    }

    public static isFlashRoulette(gameId: GameId): boolean {
        return gameId === GameId.ROULETTE_FLASH_LT;
    }

    public static isRgsRoulette(gameId: GameId): boolean {
        return gameId === GameId.RGS_ROULETTE_EURO;
    }

    public static isStarzle(gameId: GameId): boolean {
        return gameId === GameId.STARZLE;
    }

    public static isGiza(gameId: GameId): boolean {
        return gameId === GameId.GIZA;
    }

    public static isSkywardGame(gameId: GameId): boolean {
        return ClassicGameDefinition.SKYWARD_GAMES_IDS.includes(gameId);
    }

    public static isGameWithPayoutPopup(gameId: GameId): boolean {
        return ClassicGameDefinition.GAMES_WITH_PAYOUT_POPUP.includes(gameId);
    }

    public static isMultiRoundGame(gameId: GameId): boolean {
        return [
            ...ClassicGameDefinition.HISTORY_ROUNDS_GAMES_IDS,
            ...ClassicGameDefinition.SHOW_GAMES_IDS,
        ].includes(gameId);
    }

    public static isGameWithoutResults(gameId: GameId): boolean {
        return ClassicGameDefinition.GAMES_WITHOUT_RESULTS.includes(gameId);
    }
}
