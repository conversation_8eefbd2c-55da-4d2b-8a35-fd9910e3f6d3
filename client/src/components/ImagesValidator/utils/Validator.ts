import { DEFAULT_BRANDED, STATIC_SERVER_IMAGE_URL } from '../../../constants';
import { GameId, ImageFormat, ImageResolution } from '../../../enums';
import {
    IMAGE_CONFIG,
    IMAGE_CONFIG_FILENAMES,
    IMAGE_CONFIG_BRANDED,
    IImageConfigKey,
} from './config';
import { ClassicGameDefinition } from './ClassicGameDefinition.ts';

class Validator {
    private running = false;

    private buildUrl(
        path: string,
        branded: string,
        filename: string,
        size: ImageResolution,
        format: ImageFormat,
    ): string {
        return `${STATIC_SERVER_IMAGE_URL}/${path}/${branded}/${filename}-${size}.${format}`;
    }

    private async loadImage(url: string): Promise<boolean> {
        return await new Promise((resolve) => {
            const image = new Image();
            image.src = url;
            image.onload = () => {
                console.log('Image loaded:', image.src);
                resolve(true);
            };
            image.onerror = () => {
                console.error('Image failed to load:', image.src);
                resolve(false);
            };
        });
    }

    public createList(): string[] {
        return Object.entries(IMAGE_CONFIG).reduce<string[]>((acc, [key, config]) => {
            for (const filename of IMAGE_CONFIG_FILENAMES[key as IImageConfigKey]) {
                for (const size of config.sizes) {
                    for (const format of config.formats) {
                        acc.push(
                            this.buildUrl(config.path, DEFAULT_BRANDED, filename, size, format),
                        );
                    }
                }
            }

            const brandedConfig = IMAGE_CONFIG_BRANDED[key as IImageConfigKey];

            if (brandedConfig) {
                for (const [gameId, brandedList] of Object.entries(brandedConfig)) {
                    for (const branded of brandedList) {
                        for (const size of config.sizes) {
                            for (const format of config.formats) {
                                acc.push(
                                    this.buildUrl(
                                        config.path,
                                        branded,
                                        ClassicGameDefinition.GAMES_NAMES[Number(gameId) as GameId],
                                        size,
                                        format,
                                    ),
                                );
                            }
                        }
                    }
                }
            }

            return acc;
        }, []);
    }

    public async start(
        list: string[],
        callback: (url: string, status: boolean) => void,
    ): Promise<void> {
        this.running = true;

        for (let i = 0; i < list.length; i++) {
            if (!this.running) {
                return;
            }

            const url = list[i];

            await this.loadImage(url).then((status) => {
                callback(url, status);
            });
        }

        this.running = false;
    }

    public stop(): void {
        this.running = false;
    }
}

export const validator = new Validator();
