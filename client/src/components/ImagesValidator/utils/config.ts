import { GameId, ImageFormat, ImageResolution } from '../../../enums';
import { getBranded, hasSmallCurtain } from './getBranded';
import { ClassicGameDefinition } from './ClassicGameDefinition.ts';

// Copy content from "classic-iframe-webapp/libs/shared/src/constants/image.ts" START
export type IImageConfigKey =
    | 'GAME_CARDS'
    | 'CURTAIN'
    | 'CURTAIN_SMALL'
    | 'GAMIFICATION_WIZARD'
    | 'GAME_BACKGROUND'
    | 'GIZA_SECTORS'
    | 'SPLASH_SCREEN_TITLE'
    | 'SPLASH_SCREEN_TITLE_SMALL'
    | 'SPLASH_SCREEN_BACKGROUND_PORTRAIT'
    | 'SPLASH_SCREEN_BACKGROUND_LANDSCAPE'
    | 'SPLASH_SCREEN_RECT';

export interface IImageConfigValue {
    path: string;
    sizes: number[];
    formats: ImageFormat[];
    media?(sizes: ImageResolution[], index: number): string;
}

type IImageConfig = Record<IImageConfigKey, IImageConfigValue>;

export const IMAGE_CONFIG: IImageConfig = {
    GAME_CARDS: {
        path: 'game-cards',
        sizes: [ImageResolution.SIZE_320],
        formats: [ImageFormat.AVIF, ImageFormat.WEBP, ImageFormat.JPG],
    },
    CURTAIN: {
        path: 'curtains/large',
        sizes: [
            ImageResolution.SIZE_640,
            ImageResolution.SIZE_800,
            ImageResolution.SIZE_1024,
            ImageResolution.SIZE_1280,
            ImageResolution.SIZE_1600,
            ImageResolution.SIZE_1920,
        ],
        formats: [ImageFormat.AVIF, ImageFormat.WEBP, ImageFormat.JPG],
        media: (sizes, index) =>
            sizes[index] === ImageResolution.SIZE_1920
                ? `(min-width: ${Math.ceil(ImageResolution.SIZE_1600 / 2)}px)`
                : `(max-width: ${Math.ceil(sizes[index] / 2) - 1}px)`,
    },
    CURTAIN_SMALL: {
        path: 'curtains/small',
        sizes: [ImageResolution.SIZE_480, ImageResolution.SIZE_640, ImageResolution.SIZE_800],
        formats: [ImageFormat.AVIF, ImageFormat.WEBP, ImageFormat.JPG],
    },
    GAMIFICATION_WIZARD: {
        path: 'gamification/wizard',
        sizes: [ImageResolution.SIZE_320, ImageResolution.SIZE_480, ImageResolution.SIZE_640],
        formats: [ImageFormat.AVIF, ImageFormat.WEBP, ImageFormat.JPG],
    },
    GAME_BACKGROUND: {
        path: 'game-backgrounds',
        sizes: [
            ImageResolution.SIZE_640,
            ImageResolution.SIZE_800,
            ImageResolution.SIZE_1024,
            ImageResolution.SIZE_1280,
            ImageResolution.SIZE_1600,
            ImageResolution.SIZE_1920,
        ],
        formats: [ImageFormat.AVIF, ImageFormat.WEBP, ImageFormat.JPG],
        media: (sizes, index) =>
            sizes[index] === ImageResolution.SIZE_1920
                ? `(min-width: ${Math.ceil(ImageResolution.SIZE_1600 / 2)}px)`
                : `(max-width: ${Math.ceil(sizes[index] / 2) - 1}px)`,
    },
    GIZA_SECTORS: {
        path: 'giza-sectors',
        sizes: [ImageResolution.SIZE_320],
        formats: [ImageFormat.AVIF, ImageFormat.WEBP, ImageFormat.PNG],
    },
    SPLASH_SCREEN_TITLE: {
        path: 'lenny/splashscreen',
        sizes: [ImageResolution.SIZE_640],
        formats: [ImageFormat.AVIF, ImageFormat.WEBP, ImageFormat.PNG],
    },
    SPLASH_SCREEN_TITLE_SMALL: {
        path: 'lenny/splashscreen',
        sizes: [ImageResolution.SIZE_320, ImageResolution.SIZE_480, ImageResolution.SIZE_640],
        formats: [ImageFormat.AVIF, ImageFormat.WEBP, ImageFormat.PNG],
    },
    SPLASH_SCREEN_BACKGROUND_PORTRAIT: {
        path: 'lenny/splashscreen',
        sizes: [ImageResolution.SIZE_320, ImageResolution.SIZE_480, ImageResolution.SIZE_640],
        formats: [ImageFormat.AVIF, ImageFormat.WEBP, ImageFormat.JPG],
    },
    SPLASH_SCREEN_BACKGROUND_LANDSCAPE: {
        path: 'lenny/splashscreen',
        sizes: [
            ImageResolution.SIZE_640,
            ImageResolution.SIZE_800,
            ImageResolution.SIZE_1024,
            ImageResolution.SIZE_1280,
            ImageResolution.SIZE_1600,
            ImageResolution.SIZE_1920,
        ],
        formats: [ImageFormat.AVIF, ImageFormat.WEBP, ImageFormat.JPG],
    },
    SPLASH_SCREEN_RECT: {
        path: 'lenny/splashscreen',
        sizes: [
            ImageResolution.SIZE_320,
            ImageResolution.SIZE_480,
            ImageResolution.SIZE_640,
            ImageResolution.SIZE_800,
            ImageResolution.SIZE_1024,
        ],
        formats: [ImageFormat.AVIF, ImageFormat.WEBP, ImageFormat.PNG],
    },
};
// Copy content from "classic-iframe-webapp/libs/shared/src/constants/image.ts" END

export const IMAGE_CONFIG_FILENAMES: Record<IImageConfigKey, string[]> = {
    GAME_CARDS: Object.values(ClassicGameDefinition.GAMES_NAMES),
    CURTAIN: Object.values(ClassicGameDefinition.GAMES_NAMES),
    CURTAIN_SMALL: [
        ClassicGameDefinition.GAMES_NAMES[GameId.RNG_ROULETTE],
        ClassicGameDefinition.GAMES_NAMES[GameId.ROULETTE_EURO_GE],
        ClassicGameDefinition.GAMES_NAMES[GameId.ROULETTE_EURO_LT],
        ClassicGameDefinition.GAMES_NAMES[GameId.ROULETTE_AMERICAN_GE],
        ClassicGameDefinition.GAMES_NAMES[GameId.ROULETTE_FLASH_LT],
    ],
    GAMIFICATION_WIZARD: ['step_1', 'step_2', 'step_3', 'step_4'],
    GAME_BACKGROUND: [
        ClassicGameDefinition.GAMES_NAMES[GameId.LUCKY_KICKS],
        ClassicGameDefinition.GAMES_NAMES[GameId.SHOVA52],
    ],
    GIZA_SECTORS: [
        'sector-1',
        'sector-2',
        'sector-3',
        'sector-4',
        'sector-5',
        'sector-6',
        'sector-7',
        'sector-8',
        'sector-9',
        'sector-10',
        'sector-11',
        'sector-12',
        'sector-13',
        'sector-14',
        'sector-15',
        'bonusMain',
        'bonusAlpha',
        'bonusBeta',
    ],
    SPLASH_SCREEN_TITLE: ['splashscreen_title'],
    SPLASH_SCREEN_TITLE_SMALL: ['splashscreen_title_small'],
    SPLASH_SCREEN_BACKGROUND_PORTRAIT: ['splashscreen_background_portrait'],
    SPLASH_SCREEN_BACKGROUND_LANDSCAPE: ['splashscreen_background_landscape'],
    SPLASH_SCREEN_RECT: ['splashscreen_rect_left', 'splashscreen_rect_right'],
};

export const IMAGE_CONFIG_BRANDED: Partial<
    Record<IImageConfigKey, Partial<Record<GameId, string[]>>>
> = {
    GAME_CARDS: getBranded('card'),
    CURTAIN: getBranded('curtain'),
    CURTAIN_SMALL: getBranded('curtain', hasSmallCurtain),
};
