import { GameDefinition } from 'betgames-contract-js';
import brandedConfigData from './brandedConfig.json';
import { GameId } from '../../../enums';

export const hasSmallCurtain = (gameId: GameId): boolean =>
    GameDefinition.ROULETTE_GAMES_IDS.includes(gameId);

export const getBranded = (
    key: string,
    filterValueCallback?: (gameId: GameId) => boolean,
): Partial<Record<GameId, string[]>> => {
    return Object.values(brandedConfigData).reduce<Partial<Record<GameId, string[]>>>(
        (prev, current) => {
            // biome-ignore lint/suspicious/noExplicitAny: force
            const { brandedConfig } = current as any;
            if (brandedConfig) {
                Object.entries(brandedConfig).forEach(([gameIdString, configItem]) => {
                    const gameId = Number(gameIdString) as GameId;

                    if (
                        Number.isNaN(gameId) ||
                        typeof configItem !== 'object' ||
                        // biome-ignore lint/suspicious/noExplicitAny: force
                        (configItem as any)?.[key] === undefined
                    ) {
                        return;
                    }

                    if (filterValueCallback && !filterValueCallback(gameId)) {
                        return;
                    }

                    // biome-ignore lint/suspicious/noExplicitAny: force
                    const configValue = (configItem as any)[key];

                    if (!prev[gameId]) {
                        prev[gameId] = [];
                    }

                    prev[gameId].push(configValue);
                });
            }
            return prev;
        },
        {},
    );
};
