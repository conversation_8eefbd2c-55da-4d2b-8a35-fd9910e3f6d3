import { JSX } from 'react';
import { useForm } from '../../state/useForm';
import { useOutput } from '../../state/useOutput';
import { DEFAULT_BRANDED } from '../../constants';

export const Output = (): JSX.Element => {
    const { form, processing } = useForm();
    const output = useOutput((state) => state.output);

    let info = '';

    if (processing) {
        info = 'Processing...';
    } else if (form.images.length) {
        info = 'Images paths:\n';
        info += form.fileNames
            .map(
                (fileName) =>
                    `${form.imagesPath}/${form.branded || DEFAULT_BRANDED}/${fileName}-[...RESOLUTION].[...FORMAT]`,
            )
            .join('\n');
    } else {
        info = 'Select images\n\n';
        info += 'Images paths:\n';
        if (form.imagesPath) {
            info += `${form.imagesPath}/`;
        }
        info += `${form.branded || DEFAULT_BRANDED}/`;
        info += '[FILENAME]-[RESOLUTION].[FORMAT]';
    }

    return (
        <>
            <div className="fs-5">Log</div>
            <pre className="output alert alert-light mb-0 font-monospace output py-2">
                {output || info}
            </pre>
        </>
    );
};
