import { ImageFormat } from './enums';

export const AVAILABLE_RESOLUTIONS = [320, 480, 640, 800, 1024, 1280, 1600, 1920, 0];

export const AVAILABLE_FORMATS: ImageFormat[] = [
    ImageFormat.JPG,
    ImageFormat.PNG,
    ImageFormat.WEBP,
    ImageFormat.AVIF,
];

// same values as in "server/src/constants/constants.ts"
export const DEFAULT_QUALITIES: Record<ImageFormat, number> = {
    [ImageFormat.JPG]: 70,
    [ImageFormat.WEBP]: 80,
    [ImageFormat.AVIF]: 60,
    [ImageFormat.PNG]: 70,
};

export const MIN_QUALITY = 1;

export const MAX_QUALITY = 100;

export const API_URL = import.meta.env.VITE_API_URL;

export const DEFAULT_BRANDED = 'default';

export const STATIC_SERVER_IMAGE_URL = 'https://static.betgames.tv/iframe/images';
