import { create } from 'zustand';
import { uniq } from 'lodash-es';

interface IDownload {
    downloadList: string[];
    appendDownloadList(downloadList: IDownload['downloadList']): void;
    clearDownloadList(): void;
}

export const useDownloadList = create<IDownload>((set) => ({
    downloadList: [],
    appendDownloadList: (downloadList) =>
        set((currentState) => ({
            downloadList: uniq(currentState.downloadList.concat(downloadList)),
        })),
    clearDownloadList: () => set({ downloadList: [] }),
}));
