import { create } from 'zustand';
import { AVAILABLE_FORMATS, AVA<PERSON>ABLE_RESOLUTIONS, DEFAULT_QUALITIES } from '../constants';
import { ImageFormat } from '../enums';

export interface IFormState {
    form: {
        images: File[];
        imagesPath: string;
        branded: string;
        fileNames: string[];
        resolutions: number[];
        formats: ImageFormat[];
        qualities: Record<ImageFormat, number>;
    };
    processing: boolean;
    advanced: boolean;
}

interface IFormAction {
    updateForm(formFields: Partial<IFormState['form']>): void;
    clearForm(): void;
    setImages(images: IFormState['form']['images']): void;
    setFileName(fileName: string, index: number): void;
    setProcessing(value: IFormState['processing']): void;
    toggleAdvanced(): void;
    setQuality(format: ImageFormat, quality: number): void;
    setFormats(formats: ImageFormat[]): void;
}

const initialForm: IFormState['form'] = {
    images: [],
    imagesPath: '',
    branded: '',
    fileNames: [],
    resolutions: AVAILABLE_RESOLUTIONS.filter((resolution) => resolution !== 0),
    formats: AVAILABLE_FORMATS.filter((format) => format !== ImageFormat.PNG),
    qualities: DEFAULT_QUALITIES,
};

export const useForm = create<IFormState & IFormAction>((set, get) => ({
    form: initialForm,
    updateForm: (formFields) => {
        set((state) => ({ form: { ...state.form, ...formFields } }));
    },
    clearForm: () => {
        set({
            form: initialForm,
        });
    },
    setImages: (images) => {
        set((state) => ({
            form: {
                ...state.form,
                images,
                fileNames: images.map((image) => image.name.replace(/\..+$/, '')),
            },
        }));
    },
    setFormats: (formats: ImageFormat[]) => {
        const baseFormats = [ImageFormat.JPG, ImageFormat.PNG];
        const selectedBaseFormats = formats.filter((format) => baseFormats.includes(format));

        // check if both base formats selected
        if (selectedBaseFormats.length === baseFormats.length) {
            // jpg | png | undefined
            const currentBaseFormatSelection = get().form.formats.find((format) =>
                baseFormats.includes(format),
            );
            // jpg -> png | png -> jpg | undefined -> jpg
            const opositeFormat = baseFormats.find(
                (format) => format !== currentBaseFormatSelection,
            )!;
            const restFormats = formats.filter((format) => !baseFormats.includes(format));

            set((state) => ({
                form: {
                    ...state.form,
                    formats: [opositeFormat, ...restFormats],
                },
            }));
        } else {
            set((state) => ({
                form: {
                    ...state.form,
                    formats,
                },
            }));
        }
    },
    setFileName: (fileName, index) => {
        set((state) => {
            const newFileNames = [...state.form.fileNames];
            newFileNames.splice(index, 1, fileName);

            return {
                form: {
                    ...state.form,
                    fileNames: newFileNames,
                },
            };
        });
    },
    setQuality: (format, quality) => {
        const newQualities = { ...get().form.qualities };
        newQualities[format] = quality;

        set((state) => ({
            form: {
                ...state.form,
                qualities: newQualities,
            },
        }));
    },
    processing: false,
    setProcessing: (value) => {
        set({ processing: value });
    },
    advanced: false,
    toggleAdvanced: () => {
        set({ advanced: !get().advanced });
    },
}));
