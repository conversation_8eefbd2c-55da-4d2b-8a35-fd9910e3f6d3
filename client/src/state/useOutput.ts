import { create } from 'zustand';

interface IOutput {
    output: string | null;
    setOutput(output: IOutput['output']): void;
    appendOutput(output: IOutput['output']): void;
    clearOutput(): void;
}

export const useOutput = create<IOutput>((set) => ({
    output: null,
    setOutput: (output) => set({ output }),
    appendOutput: (output) =>
        set((currentState) => ({ output: `${currentState.output}${output}` })),
    clearOutput: () => set({ output: null }),
}));
