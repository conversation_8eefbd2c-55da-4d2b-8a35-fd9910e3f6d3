module.exports = {
    apps: [
        {
            name: 'client',
            cwd: '/home/<USER>/www/images-optimization/current/client',
            script: 'pnpm',
            args: 'run start',
            interpreter: 'none',
            instances: 1,
            autorestart: true,
            env: {
                NODE_ENV: 'development',
            },
            env_production: {
                NODE_ENV: 'production',
            },
        },
        {
            name: 'server',
            cwd: '/home/<USER>/www/images-optimization/current/server',
            script: 'pnpm',
            args: 'run start:server',
            interpreter: 'none',
            instances: 1,
            autorestart: true,
            env: {
                NODE_ENV: 'development',
            },
            env_production: {
                NODE_ENV: 'production',
            },
        },
    ],
};
