{"$schema": "https://biomejs.dev/schemas/2.2.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 4, "lineEnding": "lf", "lineWidth": 100, "attributePosition": "auto", "bracketSameLine": false, "bracketSpacing": true, "expand": "auto", "useEditorconfig": true}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noNonNullAssertion": "off", "useImportType": "off"}, "correctness": {"useExhaustiveDependencies": "off"}, "a11y": {"noLabelWithoutControl": "off", "noStaticElementInteractions": "off"}, "performance": {"noAccumulatingSpread": "off"}}}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "all", "semicolons": "always", "arrowParentheses": "always", "bracketSameLine": false, "quoteStyle": "single", "attributePosition": "auto", "bracketSpacing": true}}, "json": {"formatter": {"indentStyle": "space"}}, "html": {"formatter": {"selfCloseVoidElements": "always"}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": "off"}}}}