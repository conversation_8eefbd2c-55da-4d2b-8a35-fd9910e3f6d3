{"name": "images-optimisation-server", "private": true, "version": "2.0.0", "main": "src/server.ts", "scripts": {"build": "pnpm i && rm -rf dist && tsc -b", "start:server": "pnpm build && node dist/server.js", "start:server:dev": "nodemon src/server.ts", "start:ftp-server": "ts-node src/ftpServer.ts", "start:ftp-server:dev": "nodemon src/ftpServer.ts", "lint": "biome lint", "lint:ts": "tsc --noEmit --skipLib<PERSON>heck --incremental --project ./tsconfig.json", "migrate": "biome migrate --write"}, "dependencies": {"archiver": "7.0.1", "basic-ftp": "5.0.5", "cors": "2.8.5", "dotenv": "17.2.3", "express": "5.1.0", "ftp-srv": "4.6.3", "multer": "2.0.2", "sharp": "0.34.4", "ts-node": "10.9.2"}, "devDependencies": {"@biomejs/biome": "2.2.4", "@types/archiver": "6.0.3", "@types/cors": "2.8.19", "@types/express": "5.0.3", "@types/multer": "2.0.0", "@types/node": "24.6.1", "nodemon": "3.1.10", "typescript": "5.9.3"}, "packageManager": "pnpm@10.14.0"}