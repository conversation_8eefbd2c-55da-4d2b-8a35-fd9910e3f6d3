import { AvifOptions, JpegOptions, PngOptions, WebpOptions } from 'sharp';
import { IFormat, IFormats, IQualities } from './interfaces';

export const AVAILABLE_RESOLUTIONS = [320, 480, 640, 800, 1024, 1280, 1600, 1920, 0];

export const AVAILABLE_FORMATS: IFormats = ['jpg', 'png', 'webp', 'avif'];

export const DEFAULT_QUALITIES: Partial<IQualities> = {
    jpg: 70,
    webp: 80,
    avif: 60,
    png: 100,
};

export const MIN_QUALITY = 1;

export const MAX_QUALITY = 100;

export const PORT = process.env.PORT || 3000;

export const DEFAULT_BRANDED = 'default';

export const OPTIONS_BY_FORMAT: Partial<
    Record<IFormat, JpegOptions | PngOptions | WebpOptions | AvifOptions>
> = {
    jpg: {
        quality: DEFAULT_QUALITIES.jpg,
    },
    webp: {
        quality: DEFAULT_QUALITIES.webp,
        effort: 5,
    },
    avif: {
        quality: DEFAULT_QUALITIES.avif,
        effort: 6,
    },
    png: {
        compressionLevel: 9,
        quality: DEFAULT_QUALITIES.png,
        effort: 10,
        palette: true,
    },
};

export const FTP_HOST = '0.0.0.0';

export const FTP_URL = `ftp://${FTP_HOST}:${process.env.FTP_PORT}`;
