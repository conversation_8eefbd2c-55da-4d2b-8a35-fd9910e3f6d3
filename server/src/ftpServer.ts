// Mock FTP server

import 'dotenv/config';
import FTPSrv from 'ftp-srv';
import { FTP_URL } from './constants';

const ftpServer = new FTPSrv({
    url: FTP_URL,
    anonymous: true,
});

ftpServer.on('login', ({ username, password }, resolve, reject) => {
    if (username === process.env.FTP_USERNAME && password === process.env.FTP_PASSWORD) {
        resolve({ root: '../ftp-root' });
    } else {
        reject(new Error('Invalid username or password'));
    }
});

ftpServer
    .listen()
    .then(() => {
        console.log(`FTP server is running on ${FTP_URL}`);
    })
    .catch((err: Error) => {
        console.error('Error starting FTP server:', err);
    });
