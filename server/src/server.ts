import 'dotenv/config';
import fs from 'node:fs';
import { stat } from 'node:fs/promises';
import path from 'node:path';
import archiver from 'archiver';
import { Client } from 'basic-ftp';
import cors from 'cors';
import express, { type Request, type Response } from 'express';
import multer from 'multer';
import sharp from 'sharp';
import { FTP_HOST, OPTIONS_BY_FORMAT, PORT } from './constants';
import { validateFields, validateImages } from './utils/validateFields';

const app = express();

const UPLOAD_TO_FTP = false;

const upload = multer({ dest: 'uploads/', limits: { fileSize: 20 * 1024 * 1024 } });
const distName = 'public/images';
const distOutputDir = path.join(__dirname, '..', distName);

app.use(cors({ exposedHeaders: ['Content-Length', 'Content-Disposition'] }));
app.use(express.json());
app.use('/iframe/images', express.static(distOutputDir));

app.post('/upload', upload.array('images[]'), async (req: Request, res: Response) => {
    if (!req.files?.length) {
        res.status(500).json({ error: 'No images uploaded.' });
        return;
    }

    const {
        imagesPath: baseImagesPath,
        fileNames,
        branded,
        resolutions,
        formats,
        qualities,
    } = validateFields(req);

    if (!resolutions.length) {
        res.status(500).json({ error: 'No resolution selected.' });
        return;
    }

    if (!formats.length) {
        res.status(500).json({ error: 'No formats selected.' });
        return;
    }

    const imagesPath = path.join(baseImagesPath, branded);
    const absoluteOutputPath = path.join(distOutputDir, imagesPath);

    // create recursive directories if it doesn't exist
    try {
        if (!fs.existsSync(absoluteOutputPath)) {
            fs.mkdirSync(absoluteOutputPath, { recursive: true });
        }
    } catch (err) {
        console.error(err);
    }

    try {
        const convertedImages: string[] = [];

        for (let i = 0; i < (req.files as Express.Multer.File[]).length; i++) {
            const file = (req.files as Express.Multer.File[])[i];
            const inputPath = file.path;
            const baseFilename = path.parse(file.originalname).name;

            for (const resolution of resolutions) {
                for (const format of formats) {
                    const shouldResize = resolution !== 0;

                    const filename = `${fileNames[i] || baseFilename}${shouldResize ? `-${resolution}` : ''}.${format}`;
                    const outputPath = path.join(absoluteOutputPath, filename);

                    let sharpProcess = sharp(inputPath);

                    if (shouldResize) {
                        sharpProcess = sharpProcess.resize({
                            width: resolution,
                            withoutEnlargement: true,
                        });
                    }

                    await sharpProcess
                        .toFormat(format, {
                            ...OPTIONS_BY_FORMAT[format],
                            quality: qualities[format],
                        })
                        .toFile(outputPath);

                    convertedImages.push(path.join(imagesPath, filename));
                }
            }

            // Remove the original uploaded file
            fs.unlinkSync(inputPath);
        }

        if (UPLOAD_TO_FTP) {
            // upload to ftp
            const client = new Client();
            client.ftp.verbose = true;

            // login to ftp
            await client.access({
                host: FTP_HOST,
                port: Number(process.env.FTP_PORT),
                user: process.env.FTP_USERNAME,
                password: process.env.FTP_PASSWORD,
                secure: false,
            });

            const remotePathCreated: string[] = [];

            for (const file of convertedImages) {
                // create remote directories if doesn't exists
                const remotePath = path.dirname(file);
                if (!remotePathCreated.includes(remotePath)) {
                    await client.ensureDir(remotePath);
                    await client.cd('/');
                    remotePathCreated.push(remotePath);
                }

                const localFilePath = path.join(distOutputDir, file);

                await client.uploadFrom(localFilePath, file);
            }
        }

        res.json({ success: true, files: convertedImages });
    } catch (err) {
        console.error(err);
        res.status(500).json({
            error: `Failed to process images. ${(err as Error).toString()}`,
        });
    }
});

app.post('/download', async (req: Request, res: Response) => {
    try {
        const images = validateImages(req);

        if (!images.length) {
            res.status(500).json({ error: 'No files passed.' });
            return;
        }

        const archive = archiver('zip', {
            zlib: { level: 9 }, // Maximum compression
        });

        // Set the headers
        res.attachment('images.zip');
        archive.pipe(res);

        // Add files to the archive
        for (const file of images) {
            const filePath = path.join(distOutputDir, file);

            if ((await stat(filePath)).isFile()) {
                archive.file(filePath, { name: file });
            }
        }

        // Finalize the archive
        await archive.finalize();
    } catch (error) {
        console.error('Error creating zip:', error);
        res.status(500).json({ error: 'Failed to create zip file' });
    }
});

const server = app.listen(PORT, () => {
    console.log(`Server is running on http://localhost:${PORT}`);
});

server.setTimeout(1000 * 60 * 5); // 5 min
