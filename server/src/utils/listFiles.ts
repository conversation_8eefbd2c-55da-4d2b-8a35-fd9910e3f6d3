import fs from 'node:fs';
import path from 'node:path';
import { AVAILABLE_FORMATS } from '../constants';

const extensions = AVAILABLE_FORMATS.map((format) => `.${format}`);

export const listFiles = (dir: string, baseDir: string, fileList: string[] = []): string[] => {
    const files = fs.readdirSync(dir);

    files.forEach((file) => {
        const filePath = path.join(dir, file);

        if (fs.statSync(filePath).isDirectory()) {
            listFiles(filePath, baseDir, fileList);
        } else if (extensions.includes(path.extname(file).toLowerCase())) {
            fileList.push(path.relative(baseDir, filePath));
        }
    });

    return fileList;
};
