import path from 'node:path';
import { Request } from 'express';
import {
    AVAILABLE_FORMATS,
    AVAILABLE_RESOLUTIONS,
    DEFAULT_BRANDED,
    DEFAULT_QUALITIES,
    MAX_QUALITY,
    MIN_QUALITY,
} from '../constants';
import { IFormat, IFormats, IQualities } from '../interfaces';
import { simplifyFileName } from './simplifyFileName';

interface IValidateFields {
    imagesPath: string;
    fileNames: string[];
    branded: string;
    resolutions: number[];
    formats: IFormats;
    qualities: Partial<IQualities>;
}

export const validateFields = (req: Request): IValidateFields => {
    const imagesPath = path.join(req.body.imagesPath?.trim()?.replace(/\.\.\/?/g, ''));
    const fileNames = Array.isArray(req.body.fileNames) ? req.body.fileNames : [];
    const branded = simplifyFileName(req.body.branded) || DEFAULT_BRANDED;
    const resolutions = ((req.body?.resolutions || []) as string[])
        .map(Number)
        .filter((resolution) => AVAILABLE_RESOLUTIONS.includes(Number(resolution)));

    const formats = ((req.body?.formats || []) as IFormats).filter((format) =>
        AVAILABLE_FORMATS.includes(format),
    );

    const qualities = DEFAULT_QUALITIES;

    if (req.body?.qualities) {
        (Object.entries(req.body?.qualities) as [IFormat, number][]).forEach(
            ([format, quality]) => {
                if (
                    AVAILABLE_FORMATS.includes(format) &&
                    !Number.isNaN(quality) &&
                    quality >= MIN_QUALITY &&
                    quality <= MAX_QUALITY
                ) {
                    qualities[format] = Number(quality);
                }
            },
        );
    }

    return {
        imagesPath,
        fileNames,
        branded,
        resolutions,
        formats,
        qualities,
    };
};

export const validateImages = (req: Request): string[] => {
    if (!Array.isArray(req.body?.images)) {
        return [];
    }

    return req.body.images;
};
